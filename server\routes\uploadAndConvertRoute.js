import {
  extractTextFromFile,
  sanitizeExtractedText,
} from "../services/fileProcessor.js";
import { convertTextToGig } from "../services/gigConversionService.js";
import { asyncHandler } from "../middleware/errorHandler.js";

export default asyncHandler(async (req, res) => {
  // Check if file was uploaded
  if (!req.file) {
    return res.status(400).json({
      error: "No file uploaded",
      message: "Please select a file to upload",
    });
  }

  const { path: filePath, originalname } = req.file;

  try {
    // Extract text from the uploaded file
    const extractedText = await extractTextFromFile(filePath, originalname);
    console.log("Extracted text:", extractedText);
    const cleanText = sanitizeExtractedText(extractedText);
    console.log("Cleaned text:", cleanText);

    if (!cleanText || cleanText.length < 10) {
      return res.status(400).json({
        error: "Insufficient content",
        message:
          "The uploaded file does not contain enough readable text content",
      });
    }

    // Convert text to gig format using the service
    const convertResult = await convertTextToGig(cleanText);
    console.log("clean text", cleanText);
    console.log("Converted gig:", convertResult);

    // Return the converted gig data along with file info
    res.json({
      success: true,
      gig: convertResult,
      fileInfo: {
        originalName: originalname,
        size: req.file.size,
        type: req.file.mimetype,
      },
      message: "File uploaded and converted successfully",
    });
  } catch (error) {
    console.error("File upload and conversion error:", error);

    return res.status(500).json({
      error: "Processing failed",
      message:
        "An error occurred while processing your file. Please try again.",
    });
  }
});
