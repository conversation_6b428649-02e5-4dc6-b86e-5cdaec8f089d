import React, { useState, useRef } from "react";
import {
  FileUploadState,
  SUPPORTED_FILE_EXTENSIONS,
  MAX_FILE_SIZE,
} from "../types/upload";

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onUpload: (file: File) => Promise<void>;
  uploadState: FileUploadState;
  disabled?: boolean;
  className?: string;
  showProgress?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  onUpload,
  uploadState,
  disabled = false,
  className = "",
  showProgress = true,
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${Math.round(
        MAX_FILE_SIZE / (1024 * 1024)
      )}MB`;
    }

    // Check file extension
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!SUPPORTED_FILE_EXTENSIONS.includes(extension)) {
      return `File type not supported. Please upload: ${SUPPORTED_FILE_EXTENSIONS.join(
        ", "
      )}`;
    }

    return null;
  };

  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      alert(error);
      return;
    }

    setSelectedFile(file);
    onFileSelect(file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || uploadState.isUploading) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleUploadClick = async () => {
    if (selectedFile && !uploadState.isUploading) {
      await onUpload(selectedFile);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`
          border-2 border-dashed border-gray-300 rounded-lg p-8 text-center
          transition-all duration-200 bg-gray-50
          ${dragActive ? "border-blue-500 bg-blue-50" : ""}
          ${
            disabled || uploadState.isUploading
              ? "opacity-60 cursor-not-allowed"
              : "hover:border-blue-500 hover:bg-blue-50"
          }
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={SUPPORTED_FILE_EXTENSIONS.join(",")}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled || uploadState.isUploading}
        />

        {!selectedFile ? (
          <div className="flex flex-col items-center gap-4">
            <div className="text-5xl">📁</div>
            <p className="text-lg text-gray-700 m-0">
              Drag and drop your file here, or{" "}
              <button
                type="button"
                className="text-blue-500 underline bg-none border-none cursor-pointer text-inherit hover:text-blue-700 disabled:cursor-not-allowed"
                onClick={handleBrowseClick}
                disabled={disabled || uploadState.isUploading}
              >
                browse
              </button>
            </p>
            <p className="text-sm text-gray-500 m-0">
              Supported formats: {SUPPORTED_FILE_EXTENSIONS.join(", ")}
              <br />
              Maximum size: {Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB
            </p>
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-4 p-4 bg-white rounded-md border border-gray-200">
              <div className="text-3xl">📄</div>
              <div className="flex-1 text-left">
                <div className="font-medium text-gray-700">
                  {selectedFile.name}
                </div>
                <div className="text-sm text-gray-500">
                  {formatFileSize(selectedFile.size)}
                </div>
              </div>
            </div>

            {!uploadState.isUploading && !uploadState.success && (
              <div className="flex gap-2 justify-center">
                <button
                  type="button"
                  className="bg-blue-500 text-white border-none py-2 px-4 rounded-md cursor-pointer font-medium hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={handleUploadClick}
                  disabled={disabled}
                >
                  Upload File
                </button>
                <button
                  type="button"
                  className="bg-gray-100 text-gray-700 border border-gray-300 py-2 px-4 rounded-md cursor-pointer hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={handleBrowseClick}
                  disabled={disabled}
                >
                  Change File
                </button>
              </div>
            )}
          </div>
        )}

        {/* Upload Progress */}
        {showProgress && uploadState.isUploading && (
          <div className="mt-4">
            <div className="w-full h-2 bg-gray-200 rounded overflow-hidden">
              <div
                className="h-full bg-blue-500 transition-all duration-300 ease-out"
                style={{
                  width: uploadState.progress
                    ? `${uploadState.progress.percentage}%`
                    : "0%",
                }}
              />
            </div>
            <div className="mt-2 text-sm text-gray-500">
              {uploadState.progress
                ? `Uploading... ${uploadState.progress.percentage}%`
                : "Processing..."}
            </div>
          </div>
        )}

        {/* Success Message */}
        {uploadState.success && (
          <div className="flex items-center gap-2 justify-center mt-4 p-3 rounded-md bg-green-100 text-green-800">
            <div className="text-xl">✅</div>
            <div>File uploaded successfully!</div>
          </div>
        )}

        {/* Error Message */}
        {uploadState.error && (
          <div className="flex items-center gap-2 justify-center mt-4 p-3 rounded-md bg-red-100 text-red-800">
            <div className="text-xl">❌</div>
            <div>{uploadState.error}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUpload;
