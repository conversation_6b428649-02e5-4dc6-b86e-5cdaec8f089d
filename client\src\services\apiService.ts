import { Gig } from "../types/gig";
import {
  FileUploadResponse,
  FileUploadAndConvertResponse,
} from "../types/upload";
import { ExportFormat } from "../types/export";

// Define message type for chat interactions
export type MessageType = {
  id: number;
  sender: "user" | "assistant";
  text: string;
  section?: string;
  suggestion?: string;
};

// Define the response type for improve section API
interface ImproveResponse {
  suggestion: string | null;
  explanation: string | null;
}

const apiService = {
  // Base URL for API calls
  baseUrl: import.meta.env.VITE_API_URL
    ? `${import.meta.env.VITE_API_URL}/api`
    : "http://localhost:3001/api",

  // Get improvement suggestions for a specific section
  async improveSection(
    section: string,
    gigDescription: Gig,
    currentSuggestion: string
  ): Promise<ImproveResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/improve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          section,
          gigDescription,
          currentSuggestion,
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error calling improve API:", error);
      return {
        suggestion: null,
        explanation: null,
      };
    }
  },

  // Convert plain text to structured gig description
  async convertTextToGig(text: string): Promise<Gig> {
    try {
      const response = await fetch(`${this.baseUrl}/convert-text-to-gig`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include session cookies
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error calling convert-text API:", error);
      throw error;
    }
  },

  // Upload file and convert to gig in one step
  async uploadAndConvertFile(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<FileUploadAndConvertResponse> {
    const formData = new FormData();
    formData.append("file", file);

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const percentage = Math.round((event.loaded / event.total) * 100);
            onProgress(percentage);
          }
        });
      }

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error("Invalid response format"));
          }
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            reject(
              new Error(errorResponse.message || "Upload and conversion failed")
            );
          } catch {
            reject(new Error("Upload and conversion failed"));
          }
        }
      });

      xhr.addEventListener("error", () => {
        reject(new Error("Network error during upload"));
      });

      xhr.open("POST", `${this.baseUrl}/upload-and-convert`);
      xhr.withCredentials = true;
      xhr.send(formData);
    });
  },

  // Export gig data in specified format
  async exportGig(gigData: Gig, format: ExportFormat["type"]): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/export/${format}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({ gigData }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      try {
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.message || "Export failed");
      } catch {
        throw new Error("Export failed");
      }
    }

    return response.blob();
  },

  // Get available export formats
  async getExportFormats() {
    const response = await fetch(`${this.baseUrl}/export/formats`, {
      method: "GET",
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error("Failed to get export formats");
    }

    return response.json();
  },
};

export default apiService;
