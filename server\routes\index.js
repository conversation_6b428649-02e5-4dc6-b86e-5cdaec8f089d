import express from "express";
import { improveRoute } from "./improveRoute.js";
import {
  convertTextToGigRoute,
  uploadAndConvertRoute,
} from "./convertRoute.js";
import { uploadSingle, handleUploadError } from "../middleware/upload.js";
import {
  validateImproveRequest,
  validateConvertRequest,
} from "../middleware/validation.js";
import { requireAuth } from "../middleware/auth.js";
import exportRoutes from "./exportRoute.js";

const router = express.Router();

router.post("/improve", requireAuth, validateImproveRequest, improveRoute);
router.post(
  "/convert-text-to-gig",
  requireAuth,
  validateConvertRequest,
  convertTextToGigRoute
);
router.post(
  "/upload-and-convert",
  requireAuth,
  uploadSingle,
  handleUploadError,
  uploadAndConvertRoute
);
router.use("/export", exportRoutes);

export default router;
